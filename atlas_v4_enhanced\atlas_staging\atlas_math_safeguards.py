"""
A.T.L.A.S. Mathematical Safeguards Module
Comprehensive mathematical validation and error prevention
"""

import math
import logging
import numpy as np
import pandas as pd
from typing import Union, Optional, Any, List
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)


class MathematicalError(Exception):
    """Custom exception for mathematical errors"""
    pass


class AtlasMathSafeguards:
    """Comprehensive mathematical safeguards for A.T.L.A.S. system"""
    
    # Mathematical constants and limits
    MAX_SAFE_VALUE = 1e15  # Maximum safe value to prevent overflow
    MIN_SAFE_VALUE = 1e-15  # Minimum safe value to prevent underflow
    EPSILON = 1e-10  # Epsilon for zero comparisons
    
    @staticmethod
    def safe_divide(numerator: Union[float, int], denominator: Union[float, int], 
                   default: float = 0.0) -> float:
        """Safe division with zero check and overflow protection"""
        try:
            # Validate inputs
            if not isinstance(numerator, (int, float)) or not isinstance(denominator, (int, float)):
                raise MathematicalError("Division inputs must be numeric")
            
            # Check for NaN or infinite values
            if math.isnan(numerator) or math.isnan(denominator):
                logger.error("NaN value detected in division")
                return default
            
            if math.isinf(numerator) or math.isinf(denominator):
                logger.error("Infinite value detected in division")
                return default
            
            # Check for zero denominator
            if abs(denominator) < AtlasMathSafeguards.EPSILON:
                logger.warning(f"Division by zero attempted: {numerator} / {denominator}")
                return default
            
            # Perform division
            result = numerator / denominator
            
            # Check result for overflow/underflow
            if math.isinf(result) or math.isnan(result):
                logger.error(f"Division resulted in invalid value: {numerator} / {denominator} = {result}")
                return default
            
            # Check for extreme values
            if abs(result) > AtlasMathSafeguards.MAX_SAFE_VALUE:
                logger.warning(f"Division result too large: {result}, capping to max safe value")
                return AtlasMathSafeguards.MAX_SAFE_VALUE if result > 0 else -AtlasMathSafeguards.MAX_SAFE_VALUE
            
            return result
            
        except Exception as e:
            logger.error(f"Safe division error: {e}")
            return default
    
    @staticmethod
    def safe_multiply(a: Union[float, int], b: Union[float, int], 
                     default: float = 0.0) -> float:
        """Safe multiplication with overflow protection"""
        try:
            # Validate inputs
            if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
                raise MathematicalError("Multiplication inputs must be numeric")
            
            # Check for NaN or infinite values
            if math.isnan(a) or math.isnan(b) or math.isinf(a) or math.isinf(b):
                logger.error("Invalid value detected in multiplication")
                return default
            
            # Check for potential overflow before multiplication
            if abs(a) > 0 and abs(b) > AtlasMathSafeguards.MAX_SAFE_VALUE / abs(a):
                logger.warning(f"Multiplication would overflow: {a} * {b}")
                return default
            
            result = a * b
            
            # Validate result
            if math.isinf(result) or math.isnan(result):
                logger.error(f"Multiplication resulted in invalid value: {a} * {b} = {result}")
                return default
            
            return result
            
        except Exception as e:
            logger.error(f"Safe multiplication error: {e}")
            return default
    
    @staticmethod
    def safe_percentage(value: Union[float, int], total: Union[float, int], 
                       default: float = 0.0) -> float:
        """Safe percentage calculation"""
        try:
            if abs(total) < AtlasMathSafeguards.EPSILON:
                logger.warning(f"Percentage calculation with zero total: {value} / {total}")
                return default
            
            percentage = AtlasMathSafeguards.safe_divide(value, total) * 100
            
            # Validate percentage range
            if percentage < -1000 or percentage > 1000:
                logger.warning(f"Extreme percentage calculated: {percentage}%")
                return max(-1000, min(1000, percentage))
            
            return percentage
            
        except Exception as e:
            logger.error(f"Safe percentage error: {e}")
            return default
    
    @staticmethod
    def safe_sqrt(value: Union[float, int], default: float = 0.0) -> float:
        """Safe square root calculation"""
        try:
            if not isinstance(value, (int, float)):
                raise MathematicalError("Square root input must be numeric")
            
            if math.isnan(value) or math.isinf(value):
                logger.error("Invalid value for square root")
                return default
            
            if value < 0:
                logger.warning(f"Negative value for square root: {value}")
                return default
            
            result = math.sqrt(value)
            
            if math.isnan(result) or math.isinf(result):
                logger.error(f"Square root resulted in invalid value: sqrt({value}) = {result}")
                return default
            
            return result
            
        except Exception as e:
            logger.error(f"Safe square root error: {e}")
            return default
    
    @staticmethod
    def safe_log(value: Union[float, int], base: float = math.e, default: float = 0.0) -> float:
        """Safe logarithm calculation"""
        try:
            if not isinstance(value, (int, float)):
                raise MathematicalError("Logarithm input must be numeric")
            
            if math.isnan(value) or math.isinf(value) or value <= 0:
                logger.warning(f"Invalid value for logarithm: {value}")
                return default
            
            if base <= 0 or base == 1:
                logger.warning(f"Invalid base for logarithm: {base}")
                return default
            
            if base == math.e:
                result = math.log(value)
            else:
                result = math.log(value) / math.log(base)
            
            if math.isnan(result) or math.isinf(result):
                logger.error(f"Logarithm resulted in invalid value: log({value}) = {result}")
                return default
            
            return result
            
        except Exception as e:
            logger.error(f"Safe logarithm error: {e}")
            return default
    
    @staticmethod
    def validate_price(price: Union[float, int]) -> bool:
        """Validate price value for trading calculations"""
        try:
            if not isinstance(price, (int, float)):
                return False
            
            if math.isnan(price) or math.isinf(price):
                return False
            
            if price <= 0:
                return False
            
            if price > 1000000:  # $1M limit
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def validate_quantity(quantity: Union[float, int]) -> bool:
        """Validate quantity/shares for trading calculations"""
        try:
            if not isinstance(quantity, (int, float)):
                return False
            
            if math.isnan(quantity) or math.isinf(quantity):
                return False
            
            if quantity <= 0:
                return False
            
            if quantity > 1000000:  # 1M shares limit
                return False
            
            # Check if it's a reasonable integer
            if abs(quantity - round(quantity)) > 0.001:
                logger.warning(f"Non-integer quantity: {quantity}")
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def safe_dataframe_calculation(df: pd.DataFrame, operation: str, 
                                  columns: List[str], default_value: float = 0.0) -> pd.Series:
        """Safe DataFrame calculations with error handling"""
        try:
            if df.empty:
                logger.warning("Empty DataFrame for calculation")
                return pd.Series(dtype=float)
            
            # Validate columns exist
            missing_cols = [col for col in columns if col not in df.columns]
            if missing_cols:
                logger.error(f"Missing columns for calculation: {missing_cols}")
                return pd.Series([default_value] * len(df), index=df.index)
            
            # Check for NaN values
            for col in columns:
                if df[col].isna().any():
                    logger.warning(f"NaN values detected in column {col}")
                    df[col] = df[col].fillna(default_value)
            
            # Perform operation
            if operation == "add":
                result = df[columns].sum(axis=1)
            elif operation == "multiply":
                result = df[columns].prod(axis=1)
            elif operation == "divide" and len(columns) == 2:
                result = df[columns[0]] / df[columns[1]].replace(0, np.nan)
                result = result.fillna(default_value)
            elif operation == "subtract" and len(columns) == 2:
                result = df[columns[0]] - df[columns[1]]
            else:
                logger.error(f"Unsupported operation: {operation}")
                return pd.Series([default_value] * len(df), index=df.index)
            
            # Validate result
            if result.isna().any():
                logger.warning("NaN values in calculation result")
                result = result.fillna(default_value)
            
            if np.isinf(result).any():
                logger.warning("Infinite values in calculation result")
                result = result.replace([np.inf, -np.inf], default_value)
            
            return result
            
        except Exception as e:
            logger.error(f"Safe DataFrame calculation error: {e}")
            return pd.Series([default_value] * len(df), index=df.index)
    
    @staticmethod
    def safe_moving_average(data: pd.Series, window: int, default_value: float = 0.0) -> pd.Series:
        """Safe moving average calculation"""
        try:
            if data.empty or window <= 0:
                logger.warning("Invalid data or window for moving average")
                return pd.Series(dtype=float)
            
            # Remove NaN and infinite values
            clean_data = data.replace([np.inf, -np.inf], np.nan).dropna()
            
            if len(clean_data) < window:
                logger.warning(f"Insufficient data for moving average: {len(clean_data)} < {window}")
                return pd.Series([default_value] * len(data), index=data.index)
            
            # Calculate moving average
            ma = clean_data.rolling(window=window, min_periods=1).mean()
            
            # Reindex to match original data
            result = ma.reindex(data.index, fill_value=default_value)
            
            return result
            
        except Exception as e:
            logger.error(f"Safe moving average error: {e}")
            return pd.Series([default_value] * len(data), index=data.index)
    
    @staticmethod
    def validate_calculation_inputs(*args) -> bool:
        """Validate all inputs for mathematical calculations"""
        try:
            for arg in args:
                if isinstance(arg, (int, float)):
                    if math.isnan(arg) or math.isinf(arg):
                        return False
                elif isinstance(arg, (list, tuple)):
                    for item in arg:
                        if isinstance(item, (int, float)) and (math.isnan(item) or math.isinf(item)):
                            return False
                elif isinstance(arg, pd.Series):
                    if arg.isna().any() or np.isinf(arg).any():
                        return False
                elif isinstance(arg, pd.DataFrame):
                    if arg.isna().any().any() or np.isinf(arg).any().any():
                        return False
            
            return True
            
        except Exception:
            return False


# Global safeguards instance
math_safeguards = AtlasMathSafeguards()
