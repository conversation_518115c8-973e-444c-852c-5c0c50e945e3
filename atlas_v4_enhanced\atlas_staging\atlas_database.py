"""
A.T.L.A.S Database - Consolidated Database Management
Combines all database management functionality
"""

import asyncio
import logging
import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

from models import EngineStatus

logger = logging.getLogger(__name__)


# ============================================================================
# DATABASE MANAGER
# ============================================================================

class AtlasDatabaseManager:
    """Consolidated database manager for A.T.L.A.S. system"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.databases = {}
        self.connections = {}
        
        # Database configuration
        self.database_configs = {
            'main': 'atlas.db',
            'memory': 'atlas_memory.db',
            'rag': 'atlas_rag.db',
            'compliance': 'atlas_compliance.db',
            'feedback': 'atlas_feedback.db',
            'enhanced_memory': 'atlas_enhanced_memory.db'
        }
        
        logger.info("Database manager created - main: atlas.db, enhanced: 6 databases")

    async def initialize(self):
        """Initialize all databases"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Create databases directory if it doesn't exist
            os.makedirs('databases', exist_ok=True)
            
            # Initialize each database
            for db_name, db_file in self.database_configs.items():
                db_path = os.path.join('databases', db_file)
                await self._initialize_database(db_name, db_path)
            
            self.status = EngineStatus.ACTIVE
            logger.info(f"All {len(self.database_configs)} databases initialized successfully")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_database(self, db_name: str, db_path: str):
        """Initialize a single database"""
        try:
            # Create connection
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            
            # Create basic schema
            await self._create_schema(conn, db_name)
            
            # Store connection
            self.connections[db_name] = conn
            self.databases[db_name] = db_path
            
            logger.info(f"Database '{db_name}' initialized: {os.path.basename(db_path)}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database {db_name}: {e}")
            raise

    async def _create_schema(self, conn: sqlite3.Connection, db_name: str):
        """Create database schema"""
        try:
            cursor = conn.cursor()
            
            if db_name == 'main':
                # Main database schema
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        user_message TEXT NOT NULL,
                        ai_response TEXT NOT NULL,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT
                    )
                ''')
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_profiles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT UNIQUE NOT NULL,
                        profile_data TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
            elif db_name == 'memory':
                # Memory database schema
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS memories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        memory_type TEXT NOT NULL,
                        content TEXT NOT NULL,
                        importance REAL DEFAULT 0.5,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
            elif db_name == 'rag':
                # RAG database schema
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS documents (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        document_id TEXT UNIQUE NOT NULL,
                        content TEXT NOT NULL,
                        embeddings TEXT,
                        metadata TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
            elif db_name == 'compliance':
                # Compliance database schema
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS compliance_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        event_type TEXT NOT NULL,
                        description TEXT NOT NULL,
                        severity TEXT DEFAULT 'INFO',
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT
                    )
                ''')
                
            elif db_name == 'feedback':
                # Feedback database schema
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS feedback (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        feedback_type TEXT NOT NULL,
                        rating INTEGER,
                        comments TEXT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
            elif db_name == 'enhanced_memory':
                # Enhanced memory database schema
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS enhanced_memories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        memory_key TEXT UNIQUE NOT NULL,
                        memory_value TEXT NOT NULL,
                        context TEXT,
                        importance REAL DEFAULT 0.5,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
            
            conn.commit()
            logger.info(f"{db_name.title()} database schema created: {self.database_configs[db_name]}")
            
        except Exception as e:
            logger.error(f"Schema creation failed for {db_name}: {e}")
            raise

    async def store_conversation(self, session_id: str, user_message: str, ai_response: str, metadata: Dict[str, Any] = None):
        """Store conversation in main database"""
        try:
            if 'main' not in self.connections:
                return False
            
            conn = self.connections['main']
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO conversations (session_id, user_message, ai_response, metadata)
                VALUES (?, ?, ?, ?)
            ''', (session_id, user_message, ai_response, json.dumps(metadata) if metadata else None))
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to store conversation: {e}")
            return False

    async def get_conversation_history(self, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get conversation history for session"""
        try:
            if 'main' not in self.connections:
                return []
            
            conn = self.connections['main']
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT user_message, ai_response, timestamp, metadata
                FROM conversations
                WHERE session_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (session_id, limit))
            
            rows = cursor.fetchall()
            
            history = []
            for row in rows:
                history.append({
                    'user_message': row['user_message'],
                    'ai_response': row['ai_response'],
                    'timestamp': row['timestamp'],
                    'metadata': json.loads(row['metadata']) if row['metadata'] else {}
                })
            
            return list(reversed(history))  # Return in chronological order
            
        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            return []

    async def store_memory(self, memory_type: str, content: str, importance: float = 0.5):
        """Store memory in memory database"""
        try:
            if 'memory' not in self.connections:
                return False
            
            conn = self.connections['memory']
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO memories (memory_type, content, importance)
                VALUES (?, ?, ?)
            ''', (memory_type, content, importance))
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to store memory: {e}")
            return False

    async def get_memories(self, memory_type: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get memories from memory database"""
        try:
            if 'memory' not in self.connections:
                return []
            
            conn = self.connections['memory']
            cursor = conn.cursor()
            
            if memory_type:
                cursor.execute('''
                    SELECT memory_type, content, importance, created_at
                    FROM memories
                    WHERE memory_type = ?
                    ORDER BY importance DESC, created_at DESC
                    LIMIT ?
                ''', (memory_type, limit))
            else:
                cursor.execute('''
                    SELECT memory_type, content, importance, created_at
                    FROM memories
                    ORDER BY importance DESC, created_at DESC
                    LIMIT ?
                ''', (limit,))
            
            rows = cursor.fetchall()
            
            memories = []
            for row in rows:
                memories.append({
                    'memory_type': row['memory_type'],
                    'content': row['content'],
                    'importance': row['importance'],
                    'created_at': row['created_at']
                })
            
            return memories
            
        except Exception as e:
            logger.error(f"Failed to get memories: {e}")
            return []

    async def log_compliance_event(self, event_type: str, description: str, severity: str = 'INFO', metadata: Dict[str, Any] = None):
        """Log compliance event"""
        try:
            if 'compliance' not in self.connections:
                return False
            
            conn = self.connections['compliance']
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO compliance_logs (event_type, description, severity, metadata)
                VALUES (?, ?, ?, ?)
            ''', (event_type, description, severity, json.dumps(metadata) if metadata else None))
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to log compliance event: {e}")
            return False

    async def store_feedback(self, session_id: str, feedback_type: str, rating: int = None, comments: str = None):
        """Store user feedback"""
        try:
            if 'feedback' not in self.connections:
                return False
            
            conn = self.connections['feedback']
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO feedback (session_id, feedback_type, rating, comments)
                VALUES (?, ?, ?, ?)
            ''', (session_id, feedback_type, rating, comments))
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to store feedback: {e}")
            return False

    def get_database_status(self) -> Dict[str, Any]:
        """Get database status information"""
        return {
            'status': self.status.value,
            'databases': list(self.database_configs.keys()),
            'connections': len(self.connections),
            'initialized': len(self.connections) == len(self.database_configs)
        }

    async def close_connections(self):
        """Close all database connections"""
        try:
            for db_name, conn in self.connections.items():
                conn.close()
                logger.info(f"Closed connection to {db_name} database")
            
            self.connections.clear()
            self.status = EngineStatus.INACTIVE
            
        except Exception as e:
            logger.error(f"Error closing database connections: {e}")


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasDatabaseManager"
]
