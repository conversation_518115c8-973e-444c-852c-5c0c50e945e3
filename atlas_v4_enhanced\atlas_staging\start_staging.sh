#!/bin/bash
# A.T.L.A.S. Staging Environment Startup Script

echo "Starting A.T.L.A.S. Staging Environment..."
echo "Environment: STAGING"
echo "Trading Mode: PAPER TRADING ONLY"
echo "Port: 8002"
echo "Debug: Enabled"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
    echo "Virtual environment activated"
fi

# Install/update dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Run database migrations/setup if needed
echo "Setting up staging database..."
python -c "
import asyncio
from atlas_database import AtlasDatabase
async def setup_db():
    db = AtlasDatabase()
    await db.initialize()
    print('Staging database initialized')
asyncio.run(setup_db())
"

# Start the A.T.L.A.S. server
echo "Starting A.T.L.A.S. server in staging mode..."
python atlas_server.py
