"""
A.T.L.A.S Options - Consolidated Options Trading and Analysis
Combines Options Engine, Options Flow Analyzer, and Options Strategies
"""

import asyncio
import logging
import json
import sys
import os
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus

logger = logging.getLogger(__name__)


# ============================================================================
# OPTIONS DATA STRUCTURES
# ============================================================================

class OptionType(Enum):
    """Option types"""
    CALL = "call"
    PUT = "put"


class OptionStrategy(Enum):
    """Options strategies"""
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    COVERED_CALL = "covered_call"
    CASH_SECURED_PUT = "cash_secured_put"
    IRON_CONDOR = "iron_condor"
    STRADDLE = "straddle"
    STRANGLE = "strangle"


@dataclass
class OptionContract:
    """Option contract data structure"""
    symbol: str
    strike: float
    expiry: datetime
    option_type: OptionType
    price: float
    volume: int
    open_interest: int
    implied_volatility: float


@dataclass
class OptionPosition:
    """Option position data structure"""
    contract: OptionContract
    quantity: int
    entry_price: float
    current_price: float
    pnl: float
    delta: float
    gamma: float
    theta: float
    vega: float


# ============================================================================
# OPTIONS ENGINE
# ============================================================================

class AtlasOptionsEngine:
    """Comprehensive options trading and analysis engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.option_chains = {}
        self.positions = {}
        self.strategies = {}
        
        logger.info("[OPTIONS] Options Engine initialized")

    async def initialize(self):
        """Initialize options engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize Black-Scholes parameters
            self.bs_params = {
                'risk_free_rate': 0.05,  # 5% risk-free rate
                'default_volatility': 0.25  # 25% default volatility
            }
            
            # Initialize strategy templates
            await self._initialize_strategy_templates()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Options Engine ready")
            
        except Exception as e:
            logger.error(f"Options Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_strategy_templates(self):
        """Initialize options strategy templates"""
        try:
            self.strategy_templates = {
                'covered_call': {
                    'name': 'Covered Call',
                    'description': 'Own stock + sell call option',
                    'market_outlook': 'neutral to slightly bullish',
                    'max_profit': 'limited',
                    'max_loss': 'substantial',
                    'complexity': 'beginner'
                },
                'cash_secured_put': {
                    'name': 'Cash Secured Put',
                    'description': 'Sell put option with cash backing',
                    'market_outlook': 'neutral to bullish',
                    'max_profit': 'limited',
                    'max_loss': 'substantial',
                    'complexity': 'beginner'
                },
                'long_straddle': {
                    'name': 'Long Straddle',
                    'description': 'Buy call and put at same strike',
                    'market_outlook': 'high volatility expected',
                    'max_profit': 'unlimited',
                    'max_loss': 'limited',
                    'complexity': 'intermediate'
                },
                'iron_condor': {
                    'name': 'Iron Condor',
                    'description': 'Sell call spread + sell put spread',
                    'market_outlook': 'low volatility, range-bound',
                    'max_profit': 'limited',
                    'max_loss': 'limited',
                    'complexity': 'advanced'
                }
            }
            
            logger.info(f"[STRATEGIES] {len(self.strategy_templates)} options strategies loaded")
            
        except Exception as e:
            logger.error(f"Strategy template initialization failed: {e}")
            raise

    async def calculate_option_price(self, underlying_price: float, strike_price: float,
                                   time_to_expiry: float, volatility: float = None,
                                   option_type: OptionType = OptionType.CALL) -> Dict[str, Any]:
        """Calculate option price using Black-Scholes model"""
        try:
            if volatility is None:
                volatility = self.bs_params['default_volatility']
            
            risk_free_rate = self.bs_params['risk_free_rate']
            
            # Black-Scholes calculation
            d1 = (math.log(underlying_price / strike_price) + 
                  (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * math.sqrt(time_to_expiry))
            d2 = d1 - volatility * math.sqrt(time_to_expiry)
            
            # Normal CDF approximation
            def norm_cdf(x):
                return 0.5 * (1 + math.erf(x / math.sqrt(2)))
            
            if option_type == OptionType.CALL:
                price = (underlying_price * norm_cdf(d1) - 
                        strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm_cdf(d2))
            else:  # PUT
                price = (strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm_cdf(-d2) - 
                        underlying_price * norm_cdf(-d1))
            
            # Calculate Greeks
            delta = norm_cdf(d1) if option_type == OptionType.CALL else norm_cdf(d1) - 1
            gamma = math.exp(-d1**2/2) / (underlying_price * volatility * math.sqrt(2 * math.pi * time_to_expiry))
            theta = -(underlying_price * volatility * math.exp(-d1**2/2)) / (2 * math.sqrt(2 * math.pi * time_to_expiry)) - \
                    risk_free_rate * strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm_cdf(d2 if option_type == OptionType.CALL else -d2)
            vega = underlying_price * math.sqrt(time_to_expiry) * math.exp(-d1**2/2) / math.sqrt(2 * math.pi)
            
            return {
                'option_price': max(0, price),
                'greeks': {
                    'delta': delta,
                    'gamma': gamma,
                    'theta': theta / 365,  # Daily theta
                    'vega': vega / 100  # Vega per 1% volatility change
                },
                'inputs': {
                    'underlying_price': underlying_price,
                    'strike_price': strike_price,
                    'time_to_expiry': time_to_expiry,
                    'volatility': volatility,
                    'risk_free_rate': risk_free_rate,
                    'option_type': option_type.value
                }
            }
            
        except Exception as e:
            logger.error(f"Option price calculation failed: {e}")
            return {'error': str(e)}

    async def analyze_option_strategy(self, strategy_name: str, underlying_price: float,
                                    parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze options strategy profitability"""
        try:
            if strategy_name not in self.strategy_templates:
                return {'error': f'Unknown strategy: {strategy_name}'}
            
            strategy = self.strategy_templates[strategy_name]
            
            # Simulate strategy analysis based on type
            if strategy_name == 'covered_call':
                return await self._analyze_covered_call(underlying_price, parameters)
            elif strategy_name == 'cash_secured_put':
                return await self._analyze_cash_secured_put(underlying_price, parameters)
            elif strategy_name == 'long_straddle':
                return await self._analyze_long_straddle(underlying_price, parameters)
            elif strategy_name == 'iron_condor':
                return await self._analyze_iron_condor(underlying_price, parameters)
            else:
                return {'error': f'Analysis not implemented for {strategy_name}'}
                
        except Exception as e:
            logger.error(f"Strategy analysis failed for {strategy_name}: {e}")
            return {'error': str(e)}

    async def _analyze_covered_call(self, underlying_price: float, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze covered call strategy"""
        try:
            strike_price = parameters.get('strike_price', underlying_price * 1.05)
            time_to_expiry = parameters.get('time_to_expiry', 30/365)  # 30 days
            
            # Calculate call option price
            call_result = await self.calculate_option_price(
                underlying_price, strike_price, time_to_expiry, option_type=OptionType.CALL
            )
            
            if 'error' in call_result:
                return call_result
            
            call_premium = call_result['option_price']
            
            # Calculate profit/loss scenarios
            price_range = [underlying_price * (1 + i * 0.05) for i in range(-4, 5)]  # ±20% range
            pnl_scenarios = []
            
            for price in price_range:
                stock_pnl = price - underlying_price
                option_pnl = call_premium - max(0, price - strike_price)
                total_pnl = stock_pnl + option_pnl
                
                pnl_scenarios.append({
                    'underlying_price': price,
                    'stock_pnl': stock_pnl,
                    'option_pnl': option_pnl,
                    'total_pnl': total_pnl
                })
            
            # Calculate key metrics
            max_profit = call_premium + max(0, strike_price - underlying_price)
            breakeven = underlying_price - call_premium
            
            return {
                'strategy': 'covered_call',
                'current_underlying': underlying_price,
                'strike_price': strike_price,
                'call_premium': call_premium,
                'max_profit': max_profit,
                'max_loss': underlying_price - call_premium,  # If stock goes to 0
                'breakeven': breakeven,
                'pnl_scenarios': pnl_scenarios,
                'greeks': call_result['greeks'],
                'recommendation': 'Suitable for neutral to slightly bullish outlook'
            }
            
        except Exception as e:
            logger.error(f"Covered call analysis failed: {e}")
            return {'error': str(e)}

    async def _analyze_cash_secured_put(self, underlying_price: float, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze cash secured put strategy"""
        try:
            strike_price = parameters.get('strike_price', underlying_price * 0.95)
            time_to_expiry = parameters.get('time_to_expiry', 30/365)
            
            # Calculate put option price
            put_result = await self.calculate_option_price(
                underlying_price, strike_price, time_to_expiry, option_type=OptionType.PUT
            )
            
            if 'error' in put_result:
                return put_result
            
            put_premium = put_result['option_price']
            
            return {
                'strategy': 'cash_secured_put',
                'current_underlying': underlying_price,
                'strike_price': strike_price,
                'put_premium': put_premium,
                'max_profit': put_premium,
                'max_loss': strike_price - put_premium,
                'breakeven': strike_price - put_premium,
                'cash_required': strike_price * 100,  # For 1 contract
                'greeks': put_result['greeks'],
                'recommendation': 'Suitable for neutral to bullish outlook, willing to own stock'
            }
            
        except Exception as e:
            logger.error(f"Cash secured put analysis failed: {e}")
            return {'error': str(e)}

    async def _analyze_long_straddle(self, underlying_price: float, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze long straddle strategy"""
        try:
            strike_price = parameters.get('strike_price', underlying_price)
            time_to_expiry = parameters.get('time_to_expiry', 30/365)
            
            # Calculate call and put prices
            call_result = await self.calculate_option_price(
                underlying_price, strike_price, time_to_expiry, option_type=OptionType.CALL
            )
            put_result = await self.calculate_option_price(
                underlying_price, strike_price, time_to_expiry, option_type=OptionType.PUT
            )
            
            if 'error' in call_result or 'error' in put_result:
                return {'error': 'Failed to calculate option prices'}
            
            total_premium = call_result['option_price'] + put_result['option_price']
            
            return {
                'strategy': 'long_straddle',
                'current_underlying': underlying_price,
                'strike_price': strike_price,
                'call_premium': call_result['option_price'],
                'put_premium': put_result['option_price'],
                'total_premium': total_premium,
                'max_profit': 'Unlimited',
                'max_loss': total_premium,
                'upper_breakeven': strike_price + total_premium,
                'lower_breakeven': strike_price - total_premium,
                'recommendation': 'Suitable when expecting high volatility but uncertain of direction'
            }
            
        except Exception as e:
            logger.error(f"Long straddle analysis failed: {e}")
            return {'error': str(e)}

    async def _analyze_iron_condor(self, underlying_price: float, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze iron condor strategy"""
        try:
            # Iron condor strikes (example: 10% OTM on each side)
            lower_put_strike = underlying_price * 0.90
            upper_put_strike = underlying_price * 0.95
            lower_call_strike = underlying_price * 1.05
            upper_call_strike = underlying_price * 1.10
            
            time_to_expiry = parameters.get('time_to_expiry', 30/365)
            
            # Simplified analysis (would need all 4 option prices in practice)
            net_credit = underlying_price * 0.02  # Assume 2% net credit
            max_loss = (upper_call_strike - lower_call_strike) - net_credit
            
            return {
                'strategy': 'iron_condor',
                'current_underlying': underlying_price,
                'strikes': {
                    'lower_put': lower_put_strike,
                    'upper_put': upper_put_strike,
                    'lower_call': lower_call_strike,
                    'upper_call': upper_call_strike
                },
                'net_credit': net_credit,
                'max_profit': net_credit,
                'max_loss': max_loss,
                'profit_range': [upper_put_strike, lower_call_strike],
                'recommendation': 'Suitable for low volatility, range-bound markets'
            }
            
        except Exception as e:
            logger.error(f"Iron condor analysis failed: {e}")
            return {'error': str(e)}

    async def get_options_education(self, topic: str) -> Dict[str, Any]:
        """Get educational content about options"""
        try:
            education_content = {
                'basics': {
                    'title': 'Options Basics',
                    'content': '''Options are contracts that give you the right (but not obligation) to buy or sell a stock at a specific price.

**Call Options**: Right to BUY stock at strike price
**Put Options**: Right to SELL stock at strike price

**Key Terms:**
- Strike Price: The price at which you can exercise
- Expiration: When the option expires
- Premium: Cost to buy the option
- Exercise: Actually using your right to buy/sell

**Example**: AAPL $150 Call expiring in 30 days
- You pay $3 premium
- If AAPL goes to $160, you can buy at $150
- Your profit: $160 - $150 - $3 = $7 per share'''
                },
                'greeks': {
                    'title': 'Options Greeks',
                    'content': '''Greeks measure how option prices change:

**Delta**: Price change per $1 stock move
- Call delta: 0 to 1
- Put delta: -1 to 0

**Gamma**: How fast delta changes
- Higher gamma = more risk/reward

**Theta**: Time decay per day
- Options lose value as expiration approaches

**Vega**: Price change per 1% volatility change
- Higher volatility = higher option prices'''
                },
                'strategies': {
                    'title': 'Basic Options Strategies',
                    'content': '''**Beginner Strategies:**

1. **Covered Call**: Own stock + sell call
   - Generate income from stock holdings
   - Limited upside, downside protection

2. **Cash Secured Put**: Sell put + hold cash
   - Get paid to potentially buy stock
   - Must be willing to own the stock

3. **Long Call/Put**: Buy call or put
   - Limited risk, unlimited reward potential
   - Good for directional bets'''
                }
            }
            
            if topic in education_content:
                return {
                    'topic': topic,
                    'education': education_content[topic]
                }
            else:
                return {
                    'topic': topic,
                    'available_topics': list(education_content.keys()),
                    'message': f'Topic "{topic}" not found. Available topics listed above.'
                }
                
        except Exception as e:
            logger.error(f"Options education failed for topic {topic}: {e}")
            return {'error': str(e)}


# ============================================================================
# OPTIONS FLOW ANALYZER
# ============================================================================

class AtlasOptionsFlowAnalyzer:
    """Options flow and unusual activity analyzer"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.flow_data = {}
        self.unusual_activity = []
        
        logger.info("[FLOW] Options Flow Analyzer initialized")

    async def initialize(self):
        """Initialize options flow analyzer"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Flow detection thresholds
            self.flow_thresholds = {
                'volume_threshold': 1000,
                'oi_ratio_threshold': 2.0,
                'premium_threshold': 100000  # $100k+ premium
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Options Flow Analyzer ready")
            
        except Exception as e:
            logger.error(f"Options Flow Analyzer initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def detect_unusual_flow(self, symbol: str) -> Dict[str, Any]:
        """Detect unusual options flow for symbol"""
        try:
            # Simulate options flow detection
            import random
            
            # Generate simulated flow data
            call_volume = random.randint(100, 5000)
            put_volume = random.randint(100, 3000)
            call_oi = random.randint(500, 10000)
            put_oi = random.randint(400, 8000)
            
            unusual_activities = []
            
            # Check for unusual volume
            if call_volume > self.flow_thresholds['volume_threshold']:
                unusual_activities.append({
                    'type': 'high_call_volume',
                    'description': f'Unusual call volume: {call_volume} contracts',
                    'significance': 'bullish'
                })
            
            if put_volume > self.flow_thresholds['volume_threshold']:
                unusual_activities.append({
                    'type': 'high_put_volume',
                    'description': f'Unusual put volume: {put_volume} contracts',
                    'significance': 'bearish'
                })
            
            # Calculate metrics
            total_volume = call_volume + put_volume
            call_put_ratio = call_volume / put_volume if put_volume > 0 else 0
            
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'call_volume': call_volume,
                'put_volume': put_volume,
                'call_oi': call_oi,
                'put_oi': put_oi,
                'total_volume': total_volume,
                'call_put_ratio': round(call_put_ratio, 2),
                'unusual_activities': unusual_activities,
                'sentiment': 'bullish' if call_put_ratio > 1.5 else 'bearish' if call_put_ratio < 0.7 else 'neutral'
            }
            
        except Exception as e:
            logger.error(f"Unusual flow detection failed for {symbol}: {e}")
            return {'error': str(e)}


# ============================================================================
# OPTIONS ORCHESTRATOR
# ============================================================================

class AtlasOptionsOrchestrator:
    """Main options orchestrator"""
    
    def __init__(self):
        self.options_engine = AtlasOptionsEngine()
        self.flow_analyzer = AtlasOptionsFlowAnalyzer()
        self.status = EngineStatus.INITIALIZING
        
        logger.info("[ORCHESTRATOR] Options Orchestrator initialized")

    async def initialize(self):
        """Initialize all options components"""
        try:
            await self.options_engine.initialize()
            await self.flow_analyzer.initialize()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Options Orchestrator fully initialized")
            
        except Exception as e:
            logger.error(f"Options Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasOptionsEngine",
    "AtlasOptionsFlowAnalyzer",
    "AtlasOptionsOrchestrator",
    "OptionContract",
    "OptionPosition",
    "OptionType",
    "OptionStrategy"
]
