#!/usr/bin/env python3
"""
Validate Applied Scanner Fixes
"""

import asyncio
import sys
import os
from datetime import datetime
import pytz

sys.path.append(os.path.dirname(__file__))
from atlas_realtime_scanner import AtlasRealtimeScanner
from atlas_lee_method import LeeMethodScanner

async def test_fixes():
    scanner = AtlasRealtimeScanner()
    lee_scanner = LeeMethodScanner()
    
    print('🔧 VALIDATING APPLIED FIXES')
    print('='*40)
    
    # Test market hours detection
    et_tz = pytz.timezone('US/Eastern')
    now_et = datetime.now(et_tz)
    
    print(f'Current ET Time: {now_et.strftime("%H:%M:%S %Z")}')
    
    # Test scanner logic
    should_scan = scanner._should_scan()
    market_hours = scanner.is_market_hours
    
    print(f'Market Hours Active: {market_hours}')
    print(f'Should Scanner Run: {should_scan}')
    
    # Expected behavior
    market_open = datetime.now(et_tz).replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = datetime.now(et_tz).replace(hour=16, minute=0, second=0, microsecond=0)
    expected = market_open <= now_et <= market_close
    
    print(f'Expected Market Hours: {expected}')
    print(f'Fix Working: {market_hours == expected}')
    
    if market_hours == expected:
        print('✅ MARKET HOURS FIX: SUCCESS')
    else:
        print('❌ MARKET HOURS FIX: FAILED')
    
    # Test pattern sensitivity
    print(f'\n📊 PATTERN SENSITIVITY SETTINGS:')
    print(f'Min Confidence: {lee_scanner.min_confidence_threshold}')
    print(f'Pattern Sensitivity: {lee_scanner.pattern_sensitivity}')
    print(f'Allow Weak Signals: {lee_scanner.allow_weak_signals}')
    
    if lee_scanner.min_confidence_threshold >= 0.65:
        print('✅ PATTERN SENSITIVITY: SUCCESS')
    else:
        print('❌ PATTERN SENSITIVITY: NEEDS ADJUSTMENT')
    
    # Test get_status method
    try:
        status = scanner.get_status()
        print(f'\n📡 SCANNER STATUS METHOD: ✅ SUCCESS')
        print(f'Scanner Running: {status["running"]}')
    except Exception as e:
        print(f'\n📡 SCANNER STATUS METHOD: ❌ FAILED - {e}')

if __name__ == "__main__":
    asyncio.run(test_fixes())
