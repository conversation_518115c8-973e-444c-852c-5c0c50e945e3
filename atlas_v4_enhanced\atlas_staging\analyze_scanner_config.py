#!/usr/bin/env python3
"""
Analyze current A.T.L.A.S. scanner configuration
"""

from atlas_realtime_scanner import AtlasRealtimeScanner
from sp500_symbols import get_sp500_symbols, get_core_sp500_symbols, get_high_volume_symbols

def analyze_scanner_configuration():
    """Analyze current scanner configuration"""
    
    print("=== A.T.L.A.S. SCANNER CONFIGURATION ANALYSIS ===")
    print()
    
    # Initialize scanner to get current config
    scanner = AtlasRealtimeScanner()
    
    # Symbol counts
    full_sp500 = get_sp500_symbols()
    core_sp500 = get_core_sp500_symbols()
    high_volume = get_high_volume_symbols()
    
    print("📊 SYMBOL COUNTS:")
    print(f"   Full S&P 500: {len(full_sp500)} symbols")
    print(f"   Core S&P 500: {len(core_sp500)} symbols")
    print(f"   High Volume: {len(high_volume)} symbols")
    print()
    
    # Current scanner symbols
    print("🎯 CURRENT SCANNER SYMBOLS:")
    print(f"   Total symbols being scanned: {len(scanner.scan_symbols)}")
    print(f"   Priority symbols: {len(scanner.priority_symbols)}")
    print(f"   Regular symbols: {len(scanner.regular_symbols)}")
    print()
    
    print("📋 PRIORITY SYMBOLS (Top 15):")
    for i, symbol in enumerate(scanner.priority_symbols, 1):
        print(f"   {i:2d}. {symbol}")
    print()
    
    print("📋 REGULAR SYMBOLS (Next 15):")
    for i, symbol in enumerate(scanner.regular_symbols, 1):
        print(f"   {i:2d}. {symbol}")
    print()
    
    # Check for specific symbols
    all_scanned = scanner.scan_symbols
    print("🔍 SPECIFIC SYMBOL COVERAGE:")
    key_symbols = ["PLTR", "NVDA", "TSLA", "AAPL", "MSFT", "GOOGL", "AMZN", "META"]
    for symbol in key_symbols:
        status = "✅ INCLUDED" if symbol in all_scanned else "❌ NOT INCLUDED"
        print(f"   {symbol}: {status}")
    print()
    
    # Configuration details
    config = scanner.config
    print("⚙️  SCANNER CONFIGURATION:")
    print(f"   Scan Interval: {config.scan_interval} seconds")
    print(f"   Priority Scan Interval: {config.priority_scan_interval} seconds")
    print(f"   Ultra Priority Interval: {config.ultra_priority_scan_interval} seconds")
    print(f"   Batch Size: {config.batch_size} symbols")
    print(f"   Max Concurrent Scans: {config.max_concurrent_scans}")
    print(f"   API Rate Limit: {config.api_rate_limit} per minute")
    print(f"   Min Confidence: {config.min_confidence}")
    print(f"   Pattern Sensitivity: {config.pattern_sensitivity}")
    print()
    
    # Performance analysis
    print("🚀 PERFORMANCE ANALYSIS:")
    total_symbols = len(all_scanned)
    priority_symbols = len(scanner.priority_symbols)
    regular_symbols = len(scanner.regular_symbols)
    
    # Calculate scan times
    priority_scan_time = (priority_symbols / config.max_concurrent_scans) * config.priority_scan_interval
    regular_scan_time = (regular_symbols / config.batch_size) * config.scan_interval
    
    print(f"   Priority scan cycle: ~{priority_scan_time:.1f} seconds")
    print(f"   Regular scan cycle: ~{regular_scan_time:.1f} seconds")
    print(f"   Total symbols per minute: ~{60 / config.scan_interval * config.batch_size:.0f}")
    print()
    
    print("📈 COVERAGE ANALYSIS:")
    sp500_coverage = (total_symbols / len(full_sp500)) * 100
    print(f"   S&P 500 Coverage: {sp500_coverage:.1f}% ({total_symbols}/{len(full_sp500)})")
    print(f"   Symbol Selection Strategy: High-volume + Core S&P 500 subset")
    print()
    
    # API Rate Analysis
    print("🔄 API RATE ANALYSIS:")
    scans_per_minute = 60 / config.scan_interval * config.batch_size
    api_calls_per_minute = scans_per_minute * 2  # Assuming 2 API calls per symbol
    print(f"   Estimated API calls per minute: ~{api_calls_per_minute:.0f}")
    print(f"   API rate limit: {config.api_rate_limit} per minute")
    utilization = (api_calls_per_minute / config.api_rate_limit) * 100
    print(f"   API utilization: {utilization:.1f}%")
    print()
    
    # Recommendations
    print("💡 RECOMMENDATIONS:")
    if sp500_coverage < 100:
        print("   ⚠️  Currently scanning only subset of S&P 500")
        print("   📈 Could expand to full S&P 500 coverage")
        print("   🎯 Consider increasing batch size or reducing scan interval")
    
    if utilization < 50:
        print("   ✅ API capacity available for more symbols")
        print("   📊 Could handle 2-3x more symbols with current limits")
    
    print()
    print("=== ANALYSIS COMPLETE ===")

if __name__ == "__main__":
    analyze_scanner_configuration()
