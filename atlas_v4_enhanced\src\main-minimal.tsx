import React from 'react';
import ReactDOM from 'react-dom/client';

// Minimal test component
const MinimalTestApp: React.FC = () => {
  console.log('🚀 Minimal Test App Rendering...');
  
  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)',
      color: '#ffffff',
      fontFamily: 'monospace',
      flexDirection: 'column'
    }}>
      <h1 style={{
        fontSize: '3rem',
        background: 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        marginBottom: '1rem'
      }}>
        A.T.L.A.S. MINIMAL TEST
      </h1>
      
      <p style={{ color: '#8892b0', fontSize: '1.2rem', marginBottom: '2rem' }}>
        React is working! ✅
      </p>
      
      <div style={{
        padding: '20px',
        background: '#16213e',
        borderRadius: '8px',
        border: '1px solid #00ff88'
      }}>
        <h3 style={{ color: '#00ff88', marginTop: 0 }}>Test Results:</h3>
        <ul style={{ color: '#ffffff', textAlign: 'left' }}>
          <li>✅ React library loaded</li>
          <li>✅ ReactDOM working</li>
          <li>✅ Component rendering</li>
          <li>✅ CSS styling applied</li>
          <li>✅ Console logging active</li>
        </ul>
      </div>
      
      <button 
        onClick={() => {
          console.log('🔄 Switching to full A.T.L.A.S. app...');
          alert('Minimal test successful! Check console for logs.');
        }}
        style={{
          marginTop: '20px',
          background: 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)',
          color: '#000',
          border: 'none',
          padding: '10px 20px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontWeight: 'bold'
        }}
      >
        Test Successful - Click Me!
      </button>
    </div>
  );
};

// Initialize React Application
console.log('🚀 Starting minimal React app...');

const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('❌ Root element not found');
  throw new Error('Root element not found');
}

console.log('✅ Root element found:', rootElement);

const root = ReactDOM.createRoot(rootElement);
console.log('✅ React root created');

root.render(
  <React.StrictMode>
    <MinimalTestApp />
  </React.StrictMode>
);

console.log('✅ React app rendered');

// Development Hot Module Replacement
if (import.meta.hot) {
  import.meta.hot.accept();
  console.log('✅ HMR enabled');
}
