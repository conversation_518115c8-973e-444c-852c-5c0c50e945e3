{"deployment_info": {"timestamp": "2025-07-20T12:22:16.638651", "duration_seconds": 0.569566, "staging_directory": "atlas_staging", "source_directory": "."}, "deployment_log": [{"timestamp": "2025-07-20T12:22:16.640580", "step": "Creating staging directory", "status": "INFO", "details": "Path: atlas_staging"}, {"timestamp": "2025-07-20T12:22:16.641964", "step": "Staging directory created successfully", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:22:16.642691", "step": "Copying source files to staging", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:22:17.128421", "step": "Source files copied successfully", "status": "INFO", "details": "63 Python files copied"}, {"timestamp": "2025-07-20T12:22:17.139657", "step": "Creating staging configuration", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:22:17.151331", "step": "Staging .env file created", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:22:17.162303", "step": "Staging startup script created", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:22:17.174746", "step": "Validating staging environment", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:22:17.195553", "step": "Staging environment validation passed", "status": "INFO", "details": "Files: 63 Python, 6 config"}], "summary": {"total_steps": 9, "successful_steps": 9, "errors": 0, "warnings": 0}, "next_steps": ["1. Navigate to staging directory: cd atlas_staging", "2. Configure API keys in .env file", "3. Run staging environment: ./start_staging.sh", "4. Access staging server at: http://localhost:8002", "5. Run comprehensive tests", "6. Perform user acceptance testing"]}