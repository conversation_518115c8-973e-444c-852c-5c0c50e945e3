<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Debug Console</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0f;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1a1a2e;
            border-radius: 8px;
            border: 1px solid #16213e;
            padding: 20px;
        }
        .debug-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            background: #16213e;
            border-radius: 4px;
            border-left: 4px solid #00ff88;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #00ff88;
        }
        .status-ok { color: #00ff88; }
        .status-error { color: #ff3366; }
        .status-warning { color: #ffaa00; }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            background: #0a0a0f;
            border-radius: 2px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-button {
            background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .test-button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="debug-title">A.T.L.A.S. Debug Console</h1>
        
        <div class="debug-section">
            <h3>🔍 DOM Inspection</h3>
            <div id="dom-status">Checking...</div>
            <button class="test-button" onclick="checkDOM()">Refresh DOM Check</button>
        </div>
        
        <div class="debug-section">
            <h3>🌐 Network Status</h3>
            <div id="network-status">Testing...</div>
            <button class="test-button" onclick="testNetwork()">Test Network</button>
        </div>
        
        <div class="debug-section">
            <h3>⚛️ React Status</h3>
            <div id="react-status">Checking...</div>
            <button class="test-button" onclick="testReact()">Test React</button>
        </div>
        
        <div class="debug-section">
            <h3>🎨 CSS Status</h3>
            <div id="css-status">Checking...</div>
            <button class="test-button" onclick="testCSS()">Test CSS</button>
        </div>
        
        <div class="debug-section">
            <h3>📋 Console Logs</h3>
            <div id="console-logs">
                <div class="log-entry">Debug console initialized...</div>
            </div>
            <button class="test-button" onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="debug-section">
            <h3>🚀 Quick Actions</h3>
            <button class="test-button" onclick="openMainApp()">Open Main App</button>
            <button class="test-button" onclick="openMinimalApp()">Test Minimal App</button>
            <button class="test-button" onclick="runFullDiagnostic()">Full Diagnostic</button>
        </div>
    </div>

    <script>
        let logCount = 0;
        
        function addLog(message, type = 'info') {
            const logsDiv = document.getElementById('console-logs');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.style.color = type === 'error' ? '#ff3366' : type === 'warning' ? '#ffaa00' : '#00ff88';
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsDiv.appendChild(logEntry);
            logCount++;
            
            // Keep only last 20 logs
            if (logCount > 20) {
                logsDiv.removeChild(logsDiv.firstChild);
            }
            
            // Scroll to bottom
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function checkDOM() {
            addLog('🔍 Checking DOM structure...');
            
            const rootElement = document.getElementById('root');
            const domStatus = document.getElementById('dom-status');
            
            if (rootElement) {
                const hasChildren = rootElement.children.length > 0;
                const innerHTML = rootElement.innerHTML;
                
                domStatus.innerHTML = `
                    <span class="status-ok">✅ Root element found</span><br>
                    Children: ${rootElement.children.length}<br>
                    Has content: ${hasChildren ? 'Yes' : 'No'}<br>
                    Content length: ${innerHTML.length} chars<br>
                    ${hasChildren ? '<span class="status-ok">✅ React app mounted</span>' : '<span class="status-error">❌ No React content</span>'}
                `;
                
                addLog(`DOM Check: Root found, ${rootElement.children.length} children, ${innerHTML.length} chars`);
                
                if (!hasChildren) {
                    addLog('⚠️ Root element is empty - React app not mounting', 'warning');
                }
            } else {
                domStatus.innerHTML = '<span class="status-error">❌ Root element not found</span>';
                addLog('❌ Root element not found in DOM', 'error');
            }
        }
        
        async function testNetwork() {
            addLog('🌐 Testing network connectivity...');
            const networkStatus = document.getElementById('network-status');
            
            try {
                // Test Vite dev server
                const viteResponse = await fetch('/');
                const viteOk = viteResponse.ok;
                
                // Test backend API
                let backendOk = false;
                try {
                    const backendResponse = await fetch('/api/v1/health');
                    backendOk = backendResponse.ok;
                } catch (e) {
                    backendOk = false;
                }
                
                networkStatus.innerHTML = `
                    Vite Server: ${viteOk ? '<span class="status-ok">✅ Connected</span>' : '<span class="status-error">❌ Failed</span>'}<br>
                    Backend API: ${backendOk ? '<span class="status-ok">✅ Connected</span>' : '<span class="status-error">❌ Failed</span>'}
                `;
                
                addLog(`Network: Vite ${viteOk ? 'OK' : 'FAIL'}, Backend ${backendOk ? 'OK' : 'FAIL'}`);
                
            } catch (error) {
                networkStatus.innerHTML = '<span class="status-error">❌ Network test failed</span>';
                addLog(`❌ Network test error: ${error.message}`, 'error');
            }
        }
        
        function testReact() {
            addLog('⚛️ Testing React status...');
            const reactStatus = document.getElementById('react-status');
            
            // Check if React is loaded
            const hasReact = typeof React !== 'undefined';
            const hasReactDOM = typeof ReactDOM !== 'undefined';
            
            // Check for React DevTools
            const hasDevTools = window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== undefined;
            
            // Check for Vite HMR
            const hasHMR = import.meta && import.meta.hot;
            
            reactStatus.innerHTML = `
                React Library: ${hasReact ? '<span class="status-ok">✅ Loaded</span>' : '<span class="status-error">❌ Not found</span>'}<br>
                ReactDOM: ${hasReactDOM ? '<span class="status-ok">✅ Loaded</span>' : '<span class="status-error">❌ Not found</span>'}<br>
                DevTools: ${hasDevTools ? '<span class="status-ok">✅ Available</span>' : '<span class="status-warning">⚠️ Not available</span>'}<br>
                Vite HMR: ${hasHMR ? '<span class="status-ok">✅ Active</span>' : '<span class="status-warning">⚠️ Not active</span>'}
            `;
            
            addLog(`React: ${hasReact ? 'OK' : 'MISSING'}, ReactDOM: ${hasReactDOM ? 'OK' : 'MISSING'}`);
        }
        
        function testCSS() {
            addLog('🎨 Testing CSS loading...');
            const cssStatus = document.getElementById('css-status');
            
            // Check if cyberpunk theme is loaded
            const testElement = document.createElement('div');
            testElement.style.display = 'none';
            testElement.className = 'atlas-theme-test';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasCustomCSS = computedStyle.getPropertyValue('--atlas-primary') !== '';
            
            document.body.removeChild(testElement);
            
            cssStatus.innerHTML = `
                Cyberpunk Theme: ${hasCustomCSS ? '<span class="status-ok">✅ Loaded</span>' : '<span class="status-warning">⚠️ Not detected</span>'}<br>
                Stylesheets: ${document.styleSheets.length} loaded
            `;
            
            addLog(`CSS: ${document.styleSheets.length} stylesheets, Custom theme: ${hasCustomCSS ? 'OK' : 'NOT DETECTED'}`);
        }
        
        function clearLogs() {
            document.getElementById('console-logs').innerHTML = '<div class="log-entry">Logs cleared...</div>';
            logCount = 0;
        }
        
        function openMainApp() {
            addLog('🚀 Opening main A.T.L.A.S. app...');
            window.open('/', '_blank');
        }
        
        function openMinimalApp() {
            addLog('🧪 Testing minimal app...');
            // This would need to be implemented by temporarily switching the app
            alert('Minimal app test would require code changes. Check console for React mounting issues.');
        }
        
        async function runFullDiagnostic() {
            addLog('🔬 Running full diagnostic...');
            
            checkDOM();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testNetwork();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testReact();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testCSS();
            
            addLog('✅ Full diagnostic completed');
        }
        
        // Auto-run initial checks
        window.addEventListener('load', () => {
            addLog('🚀 A.T.L.A.S. Debug Console loaded');
            setTimeout(runFullDiagnostic, 1000);
        });
        
        // Capture console errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            addLog(`Console Error: ${args.join(' ')}`, 'error');
            originalConsoleError.apply(console, args);
        };
        
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
            addLog(`Console Warning: ${args.join(' ')}`, 'warning');
            originalConsoleWarn.apply(console, args);
        };
    </script>
</body>
</html>
