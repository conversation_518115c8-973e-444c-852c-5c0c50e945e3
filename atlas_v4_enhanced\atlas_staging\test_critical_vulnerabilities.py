"""
A.T.L.A.S. Critical Vulnerabilities Test Suite
Tests for the critical issues identified in QA review
"""

import pytest
import asyncio
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import A.T.L.A.S. components
from atlas_trading_core import TradingGodEngine, AutoTradingEngine
from atlas_risk_core import AtlasRiskEngine
from atlas_lee_method import LeeMethodScanner
from atlas_market_core import AtlasMarketEngine
from atlas_ai_core import AtlasConversationalEngine
from models import Quote, Position, Order


class TestTradingLogicVulnerabilities:
    """Test critical trading logic vulnerabilities"""
    
    @pytest.mark.asyncio
    async def test_division_by_zero_position_sizing(self):
        """Test division by zero in position sizing calculations"""
        engine = TradingGodEngine()
        
        # Test case 1: Entry price equals stop price
        with patch.object(engine, '_get_real_market_data') as mock_data:
            mock_data.return_value = {'price': 100.0}
            
            # This should not crash but should handle gracefully
            analysis = await engine.generate_6_point_analysis("AAPL", "test")
            
            # Should not contain infinite or NaN values
            assert "inf" not in analysis.lower()
            assert "nan" not in analysis.lower()
    
    @pytest.mark.asyncio
    async def test_histogram_division_by_zero(self):
        """Test histogram calculations with zero values"""
        scanner = LeeMethodScanner()
        
        # Create test data with zero histogram values
        test_data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104],
            'histogram': [0.0, 0.0, 0.0, 0.0, 0.0],
            'ema5': [100, 100.5, 101, 101.5, 102],
            'ema8': [100, 100.3, 100.6, 100.9, 101.2],
            'ema21': [100, 100.1, 100.2, 100.3, 100.4],
            'squeeze_active': [True, True, True, True, True]
        })
        
        # This should not crash
        result = scanner.detect_squeeze_pattern(test_data)
        
        # Should handle zero values gracefully
        assert result is None or isinstance(result, dict)
    
    @pytest.mark.asyncio
    async def test_portfolio_value_zero_division(self):
        """Test portfolio calculations with zero portfolio value"""
        risk_engine = AtlasRiskEngine()
        
        # Set portfolio value to zero
        risk_engine.portfolio_value = 0.0
        
        # This should not crash
        assessment = await risk_engine.assess_position_risk("AAPL", 100, 150.0, 100000.0)
        
        # Should return error state, not crash
        assert assessment.risk_level == "HIGH"
        assert assessment.position_size_percent == 100.0
    
    def test_confidence_score_overflow(self):
        """Test confidence score calculations for overflow"""
        scanner = LeeMethodScanner()
        
        # Test with extreme values that could cause overflow
        confidence = scanner._calculate_enhanced_confidence(
            histogram_rising=True,
            emas_rising=True, 
            has_consecutive_decline=True,
            momentum_rising=True,
            ema5_rising=True,
            consecutive_down=10  # Extreme value
        )
        
        # Confidence should be between 0 and 1
        assert 0 <= confidence <= 1
        assert not np.isnan(confidence)
        assert not np.isinf(confidence)


class TestDataValidationVulnerabilities:
    """Test data validation and error handling vulnerabilities"""
    
    @pytest.mark.asyncio
    async def test_malformed_json_handling(self):
        """Test handling of malformed JSON responses"""
        market_engine = AtlasMarketEngine()
        
        # Mock a response that returns HTML instead of JSON
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # This should not crash
            quote = await market_engine._get_fmp_quote("AAPL")
            assert quote is None
    
    def test_invalid_symbol_extraction(self):
        """Test symbol extraction with invalid inputs"""
        ai_engine = AtlasConversationalEngine()
        
        # Test with common false positives
        test_messages = [
            "I want to buy stocks",  # "TO" should not be extracted
            "Tell me about the market",  # "THE" should not be extracted
            "Show me options for trading",  # "FOR" should not be extracted
            "What is the best stock?",  # "IS" should not be extracted
        ]
        
        for message in test_messages:
            symbols = ai_engine._extract_symbols(message)
            
            # Should not contain common false positives
            false_positives = {'TO', 'OR', 'AND', 'THE', 'FOR', 'WITH', 'BY', 'IS'}
            extracted_false_positives = set(symbols) & false_positives
            assert len(extracted_false_positives) == 0, f"False positives found: {extracted_false_positives}"
    
    @pytest.mark.asyncio
    async def test_stale_data_detection(self):
        """Test stale data detection and rejection"""
        market_engine = AtlasMarketEngine()
        
        # Create a quote with old timestamp
        old_quote = Quote(
            symbol="AAPL",
            price=150.0,
            timestamp=datetime.now() - timedelta(hours=2),  # 2 hours old
            volume=1000000
        )
        
        # Should reject stale data during market hours
        with patch.object(market_engine, '_is_market_hours', return_value=True):
            is_valid = market_engine._validate_quote_data(old_quote, 'test_source')
            assert not is_valid  # Should reject stale data
    
    def test_extreme_price_validation(self):
        """Test validation of extreme price values"""
        market_engine = AtlasMarketEngine()
        
        # Test extreme prices
        extreme_quotes = [
            Quote(symbol="AAPL", price=-10.0, timestamp=datetime.now(), volume=1000),  # Negative
            Quote(symbol="AAPL", price=0.0, timestamp=datetime.now(), volume=1000),    # Zero
            Quote(symbol="AAPL", price=2000000.0, timestamp=datetime.now(), volume=1000),  # Too high
        ]
        
        for quote in extreme_quotes:
            is_valid = market_engine._validate_quote_data(quote, 'test_source')
            assert not is_valid, f"Should reject extreme price: {quote.price}"


class TestAIResponseAccuracy:
    """Test AI response accuracy and fallback handling"""
    
    @pytest.mark.asyncio
    async def test_grok_api_failure_fallback(self):
        """Test proper fallback when Grok API fails"""
        ai_engine = AtlasConversationalEngine()
        
        # Mock Grok API failure
        with patch.object(ai_engine, '_call_grok_api', return_value=None):
            # Should not crash and should provide meaningful response
            response = await ai_engine.analyze_scanner_results({}, "test context")
            
            assert isinstance(response, str)
            assert len(response) > 0
            assert "unavailable" in response.lower() or "failed" in response.lower()
    
    @pytest.mark.asyncio
    async def test_conversation_context_preservation(self):
        """Test that critical trading context is preserved"""
        ai_engine = AtlasConversationalEngine()
        session_id = "test_session"
        
        # Simulate conversation with trading context
        await ai_engine.conversation_flow_manager.update_conversation_context(
            session_id, 
            "Analyze AAPL with $10000 position size",
            "Analysis complete for AAPL at $150"
        )
        
        context = await ai_engine.conversation_flow_manager.get_conversation_context(session_id)
        
        # Should preserve trading symbols and amounts
        assert len(context.history) > 0
        last_exchange = context.history[-1]
        assert "AAPL" in last_exchange["user_message"]
        assert "10000" in last_exchange["user_message"]


class TestConcurrencyVulnerabilities:
    """Test concurrency and race condition vulnerabilities"""
    
    @pytest.mark.asyncio
    async def test_concurrent_scanner_operations(self):
        """Test multiple simultaneous scanner operations"""
        scanner = LeeMethodScanner()
        
        # Create test data
        test_data = pd.DataFrame({
            'close': np.random.randn(100) + 100,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # Run multiple concurrent scans
        tasks = []
        for i in range(5):
            task = scanner.calculate_ttm_squeeze(test_data.copy())
            tasks.append(task)
        
        # Should complete without deadlocks or crashes
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check that no exceptions occurred
        for result in results:
            assert not isinstance(result, Exception), f"Concurrent operation failed: {result}"
    
    @pytest.mark.asyncio
    async def test_websocket_connection_limits(self):
        """Test WebSocket connection handling under load"""
        # This would test the real-time scanner WebSocket connections
        # For now, we'll test the basic structure
        
        from atlas_alert_manager import AtlasAlertManager
        alert_manager = AtlasAlertManager()
        
        # Should handle multiple alert registrations
        for i in range(10):
            await alert_manager.register_alert_callback(f"test_callback_{i}", lambda x: None)
        
        # Should not crash with many callbacks
        test_alert = {
            "type": "test",
            "symbol": "AAPL",
            "message": "Test alert"
        }
        
        # This should not crash
        alert_manager._trigger_alert(test_alert)


class TestUserSafetyCompliance:
    """Test user safety and compliance vulnerabilities"""
    
    def test_paper_trading_enforcement(self):
        """Test that paper trading mode is properly enforced"""
        trading_engine = AutoTradingEngine()
        
        # Ensure paper trading is enabled by default
        assert trading_engine.paper_trading_mode == True
        
        # Test that live trading requires explicit override
        # (This test would be expanded with actual trading execution tests)
        assert trading_engine.require_confirmation == True
    
    @pytest.mark.asyncio
    async def test_risk_limit_enforcement(self):
        """Test that risk limits are properly enforced"""
        risk_engine = AtlasRiskEngine()
        
        # Test position size limit enforcement
        large_position_assessment = await risk_engine.assess_position_risk(
            "AAPL", 10000, 150.0, 100000.0  # 150% of portfolio
        )
        
        # Should flag as high risk
        assert large_position_assessment.risk_level == "HIGH"
        
        # Test risk validation
        validation = await risk_engine.validate_trade_risk(large_position_assessment)
        
        # Should not approve high-risk trades
        assert not validation["approved"]
        assert len(validation["violations"]) > 0


class TestErrorRecovery:
    """Test error recovery and graceful degradation"""
    
    @pytest.mark.asyncio
    async def test_api_failure_recovery(self):
        """Test recovery from API failures"""
        market_engine = AtlasMarketEngine()
        
        # Mock all APIs failing
        with patch.object(market_engine, '_get_alpaca_quote', return_value=None), \
             patch.object(market_engine, '_get_fmp_quote', return_value=None), \
             patch.object(market_engine, '_get_yfinance_quote', return_value=None):
            
            # Should return None gracefully, not crash
            quote = await market_engine.get_quote("AAPL")
            assert quote is None
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self):
        """Test database error handling"""
        # This would test database connection failures
        # For now, test basic error handling structure
        
        from atlas_database import AtlasDatabase
        db = AtlasDatabase()
        
        # Should handle database initialization errors gracefully
        with patch('aiosqlite.connect', side_effect=Exception("Database error")):
            try:
                await db.initialize()
                # Should not crash, should log error
            except Exception as e:
                # Should be a controlled exception, not a crash
                assert "Database error" in str(e)


# Test runner
if __name__ == "__main__":
    # Run critical vulnerability tests
    pytest.main([__file__, "-v", "--tb=short"])
