import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';

// Test imports one by one to find the problematic component
let AtlasChatInterface: any = null;
let AtlasScannerPanel: any = null;
let AtlasStatusPanel: any = null;
let AtlasStatusDashboard: any = null;
let AtlasChartAnalysisPanel: any = null;
let AtlasHeader: any = null;
let AtlasLoadingScreen: any = null;

// Services and Hooks
let atlasApi: any = null;
let useAtlasWebSocket: any = null;
let useAtlasSystem: any = null;

// Test each import individually
try {
  console.log('🔍 Testing AtlasChatInterface import...');
  AtlasChatInterface = require('./chat/AtlasChatInterface').default;
  console.log('✅ AtlasChatInterface imported successfully');
} catch (error) {
  console.error('❌ AtlasChatInterface import failed:', error);
}

try {
  console.log('🔍 Testing AtlasScannerPanel import...');
  AtlasScannerPanel = require('./scanner/AtlasScannerPanel').default;
  console.log('✅ AtlasScannerPanel imported successfully');
} catch (error) {
  console.error('❌ AtlasScannerPanel import failed:', error);
}

try {
  console.log('🔍 Testing AtlasStatusPanel import...');
  AtlasStatusPanel = require('./status/AtlasStatusPanel').default;
  console.log('✅ AtlasStatusPanel imported successfully');
} catch (error) {
  console.error('❌ AtlasStatusPanel import failed:', error);
}

try {
  console.log('🔍 Testing AtlasStatusDashboard import...');
  AtlasStatusDashboard = require('./status/AtlasStatusDashboard').default;
  console.log('✅ AtlasStatusDashboard imported successfully');
} catch (error) {
  console.error('❌ AtlasStatusDashboard import failed:', error);
}

try {
  console.log('🔍 Testing AtlasChartAnalysisPanel import...');
  AtlasChartAnalysisPanel = require('./charts/AtlasChartAnalysisPanel').default;
  console.log('✅ AtlasChartAnalysisPanel imported successfully');
} catch (error) {
  console.error('❌ AtlasChartAnalysisPanel import failed:', error);
}

try {
  console.log('🔍 Testing AtlasHeader import...');
  AtlasHeader = require('./layout/AtlasHeader').default;
  console.log('✅ AtlasHeader imported successfully');
} catch (error) {
  console.error('❌ AtlasHeader import failed:', error);
}

try {
  console.log('🔍 Testing AtlasLoadingScreen import...');
  AtlasLoadingScreen = require('./common/AtlasLoadingScreen').default;
  console.log('✅ AtlasLoadingScreen imported successfully');
} catch (error) {
  console.error('❌ AtlasLoadingScreen import failed:', error);
}

try {
  console.log('🔍 Testing atlasApi import...');
  atlasApi = require('../services/atlasApi').atlasApi;
  console.log('✅ atlasApi imported successfully');
} catch (error) {
  console.error('❌ atlasApi import failed:', error);
}

try {
  console.log('🔍 Testing useAtlasWebSocket import...');
  useAtlasWebSocket = require('../hooks/useAtlasWebSocket').useAtlasWebSocket;
  console.log('✅ useAtlasWebSocket imported successfully');
} catch (error) {
  console.error('❌ useAtlasWebSocket import failed:', error);
}

try {
  console.log('🔍 Testing useAtlasSystem import...');
  useAtlasSystem = require('../hooks/useAtlasSystem').useAtlasSystem;
  console.log('✅ useAtlasSystem imported successfully');
} catch (error) {
  console.error('❌ useAtlasSystem import failed:', error);
}

// A.T.L.A.S. Cyberpunk Theme Configuration
const atlasCyberpunkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00ff88',
    },
    secondary: {
      main: '#0099ff',
    },
    background: {
      default: '#0a0a0f',
      paper: '#1a1a2e',
    },
    text: {
      primary: '#ffffff',
      secondary: '#8892b0',
    },
  },
});

interface AtlasAppDebugProps {
  streamChatClient?: any;
}

const AtlasAppDebug: React.FC<AtlasAppDebugProps> = ({ streamChatClient }) => {
  console.log('🚀 AtlasAppDebug component initializing...');
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [importResults, setImportResults] = useState<string[]>([]);

  useEffect(() => {
    console.log('🔍 AtlasAppDebug useEffect triggered...');
    
    const results = [];
    
    // Check which components loaded successfully
    if (AtlasChatInterface) results.push('✅ AtlasChatInterface');
    else results.push('❌ AtlasChatInterface');
    
    if (AtlasScannerPanel) results.push('✅ AtlasScannerPanel');
    else results.push('❌ AtlasScannerPanel');
    
    if (AtlasStatusPanel) results.push('✅ AtlasStatusPanel');
    else results.push('❌ AtlasStatusPanel');
    
    if (AtlasStatusDashboard) results.push('✅ AtlasStatusDashboard');
    else results.push('❌ AtlasStatusDashboard');
    
    if (AtlasChartAnalysisPanel) results.push('✅ AtlasChartAnalysisPanel');
    else results.push('❌ AtlasChartAnalysisPanel');
    
    if (AtlasHeader) results.push('✅ AtlasHeader');
    else results.push('❌ AtlasHeader');
    
    if (AtlasLoadingScreen) results.push('✅ AtlasLoadingScreen');
    else results.push('❌ AtlasLoadingScreen');
    
    if (atlasApi) results.push('✅ atlasApi');
    else results.push('❌ atlasApi');
    
    if (useAtlasWebSocket) results.push('✅ useAtlasWebSocket');
    else results.push('❌ useAtlasWebSocket');
    
    if (useAtlasSystem) results.push('✅ useAtlasSystem');
    else results.push('❌ useAtlasSystem');
    
    setImportResults(results);
    setIsInitialized(true);
  }, []);

  if (!isInitialized) {
    return (
      <ThemeProvider theme={atlasCyberpunkTheme}>
        <CssBaseline />
        <Box
          sx={{
            height: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)',
            color: '#ffffff',
            fontFamily: 'monospace',
          }}
        >
          <div>Loading debug information...</div>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={atlasCyberpunkTheme}>
      <CssBaseline />
      <Box
        sx={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)',
          color: '#ffffff',
          fontFamily: 'monospace',
          flexDirection: 'column',
          padding: 4,
        }}
      >
        <Box
          component="h1"
          sx={{
            fontSize: '2rem',
            fontWeight: 700,
            background: 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            marginBottom: 3,
          }}
        >
          A.T.L.A.S. Debug Report
        </Box>
        
        <Box
          sx={{
            background: '#1a1a2e',
            padding: 3,
            borderRadius: 2,
            border: '1px solid #16213e',
            maxWidth: 600,
            width: '100%',
          }}
        >
          <Box component="h3" sx={{ color: '#00ff88', marginBottom: 2 }}>
            Component Import Status:
          </Box>
          
          {importResults.map((result, index) => (
            <Box
              key={index}
              sx={{
                padding: 1,
                marginBottom: 1,
                background: '#16213e',
                borderRadius: 1,
                fontSize: '0.9rem',
                color: result.startsWith('✅') ? '#00ff88' : '#ff3366',
              }}
            >
              {result}
            </Box>
          ))}
          
          <Box sx={{ marginTop: 3, textAlign: 'center' }}>
            <button
              onClick={() => {
                console.log('🔄 Reloading page...');
                window.location.reload();
              }}
              style={{
                background: 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)',
                color: '#000',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontWeight: 'bold',
              }}
            >
              Reload Page
            </button>
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default AtlasAppDebug;
