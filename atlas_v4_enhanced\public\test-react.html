<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0f;
            color: #ffffff;
            font-family: monospace;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #1a1a2e;
            padding: 20px;
            border-radius: 8px;
        }
        .status { margin: 10px 0; padding: 10px; background: #16213e; border-radius: 4px; }
        .ok { color: #00ff88; }
        .error { color: #ff3366; }
        .warning { color: #ffaa00; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>React Loading Test</h1>
        
        <div class="status">
            <strong>Step 1:</strong> <span id="step1">Loading HTML...</span>
        </div>
        
        <div class="status">
            <strong>Step 2:</strong> <span id="step2">Checking root element...</span>
        </div>
        
        <div class="status">
            <strong>Step 3:</strong> <span id="step3">Loading main.tsx...</span>
        </div>
        
        <div class="status">
            <strong>Step 4:</strong> <span id="step4">Initializing React...</span>
        </div>
        
        <div class="status">
            <strong>Step 5:</strong> <span id="step5">Mounting components...</span>
        </div>
        
        <div id="test-root" style="margin-top: 20px; padding: 15px; background: #2a2a4a; border-radius: 4px;">
            <h3>Test React Mount:</h3>
            <div id="react-test-area">No React content yet...</div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testReactMount()" style="background: #00ff88; color: #000; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                Test React Mount
            </button>
            <button onclick="checkMainApp()" style="background: #0099ff; color: #fff; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                Check Main App
            </button>
        </div>
    </div>

    <script>
        let stepCount = 0;
        
        function updateStep(stepNum, message, status = 'ok') {
            const element = document.getElementById(`step${stepNum}`);
            const className = status === 'error' ? 'error' : status === 'warning' ? 'warning' : 'ok';
            element.innerHTML = `<span class="${className}">${message}</span>`;
        }
        
        function testReactMount() {
            console.log('🧪 Testing React mount...');
            
            // Step 1: HTML loaded
            updateStep(1, '✅ HTML loaded successfully', 'ok');
            
            // Step 2: Check root element
            const rootElement = document.getElementById('root');
            if (rootElement) {
                updateStep(2, '✅ Root element found', 'ok');
                console.log('Root element:', rootElement);
                console.log('Root innerHTML length:', rootElement.innerHTML.length);
                console.log('Root children:', rootElement.children.length);
            } else {
                updateStep(2, '❌ Root element not found', 'error');
                return;
            }
            
            // Step 3: Check if main.tsx is loading
            const scripts = document.querySelectorAll('script[src*="main.tsx"]');
            if (scripts.length > 0) {
                updateStep(3, '✅ main.tsx script found', 'ok');
            } else {
                updateStep(3, '⚠️ main.tsx script not found', 'warning');
            }
            
            // Step 4: Check if React is available
            setTimeout(() => {
                if (typeof React !== 'undefined') {
                    updateStep(4, '✅ React library loaded', 'ok');
                } else {
                    updateStep(4, '❌ React library not loaded', 'error');
                }
                
                // Step 5: Check if React has mounted
                setTimeout(() => {
                    if (rootElement.children.length > 0 && !rootElement.querySelector('.atlas-initial-loading')) {
                        updateStep(5, '✅ React components mounted', 'ok');
                    } else if (rootElement.querySelector('.atlas-initial-loading')) {
                        updateStep(5, '⚠️ Still showing loading screen', 'warning');
                    } else {
                        updateStep(5, '❌ React components not mounted', 'error');
                    }
                }, 2000);
            }, 1000);
        }
        
        function checkMainApp() {
            console.log('🔍 Checking main app...');
            
            // Open main app in new tab and check if it loads
            const newWindow = window.open('/', '_blank');
            
            setTimeout(() => {
                try {
                    const newDoc = newWindow.document;
                    const newRoot = newDoc.getElementById('root');
                    
                    if (newRoot) {
                        console.log('Main app root found');
                        console.log('Main app root children:', newRoot.children.length);
                        console.log('Main app root content length:', newRoot.innerHTML.length);
                        
                        document.getElementById('react-test-area').innerHTML = `
                            <strong>Main App Status:</strong><br>
                            Root element: Found<br>
                            Children: ${newRoot.children.length}<br>
                            Content length: ${newRoot.innerHTML.length} chars<br>
                            Status: ${newRoot.children.length > 0 ? '<span class="ok">✅ Mounted</span>' : '<span class="error">❌ Empty</span>'}
                        `;
                    } else {
                        document.getElementById('react-test-area').innerHTML = '<span class="error">❌ Main app root not found</span>';
                    }
                } catch (e) {
                    console.log('Cannot access main app (CORS):', e.message);
                    document.getElementById('react-test-area').innerHTML = '<span class="warning">⚠️ Cannot access main app (CORS)</span>';
                }
            }, 3000);
        }
        
        // Auto-run test when page loads
        window.addEventListener('load', () => {
            console.log('🚀 React test page loaded');
            setTimeout(testReactMount, 500);
        });
        
        // Monitor console errors
        const originalError = console.error;
        console.error = function(...args) {
            document.getElementById('react-test-area').innerHTML += `<br><span class="error">Console Error: ${args.join(' ')}</span>`;
            originalError.apply(console, args);
        };
    </script>
</body>
</html>
